<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left>
      <form [formGroup]="form" style="padding-top: 0">
        <div class="flex flex-wrap items-center">
          <label-value labelWidth="100px" label="开盒记录编号">
            <input trim type="text" formControlName="recordCode" (keydown.enter)="query()" />
          </label-value>
          <!-- <label-value labelWidth="100px" label="盲盒名称">
            <input type="text" formControlName="blindBoxName" (keydown.enter)="query()" />
          </label-value> -->
          <label-value labelWidth="100px" label="下单时间">
            <app-date-ranger
              [(ngModel)]="dateRange"
              [ngModelOptions]="{standalone: true}"
              (dateChange)="handleDateChange($event)"
            ></app-date-ranger>
          </label-value>
          <!-- <label-value labelWidth="100px" label="分销员">
            <input type="text" formControlName="distributorName" (keydown.enter)="query()" />
          </label-value>
          <label-value labelWidth="100px" label="买家姓名">
            <input type="text" formControlName="customerName" (keydown.enter)="query()" />
          </label-value>
          <label-value labelWidth="100px" label="买家手机号">
            <input type="text" formControlName="customerPhone" (keydown.enter)="query()" />
          </label-value> -->
        </div>
        <div class="form-btn-container">
          <button class="btn btn-primary mr2 mb2" type="submit" (click)="query()">查询</button>
          <button class="btn mr2 mb2" (click)="resetQueryParam()">重置</button>

          <!-- <vdr-dropdown *vdrIfPermissions="['CreateExportTask']" class="mr2 mb2">
            <button [clrLoading]="exportLoading$ | async" class="btn" vdrDropdownTrigger>导出</button>
            <vdr-dropdown-menu vdrPosition="bottom-left">
              <button vdrDropdownItem (click)="handleClickExport()">导出列表</button>
              <button vdrDropdownItem (click)="handleClickExportStatistics()">导出统计数据</button>
            </vdr-dropdown-menu>
          </vdr-dropdown> -->
          <!-- <button
            *vdrIfPermissions="['CreateExportTask']"
            [clrLoading]="exportLoading$ | async"
            class="btn mr2 mb2"
            (click)="handleClickExport()"
          >
            导出
          </button> -->
          <!-- <vdr-dropdown *vdrIfPermissions="['ReadExportTask']" class="mr2 mb2">
            <button [clrLoading]="exportLoading$ | async" class="btn" vdrDropdownTrigger>导出记录</button>
            <vdr-dropdown-menu vdrPosition="bottom-left">
              <button
                [routerLink]="['../export-task-list' + '/blindBoxOrder']"
                [queryParams]="{exportType: 'blindBoxOrder'}"
                vdrDropdownItem
              >
                列表数据记录
              </button>
              <button
                [routerLink]="['../export-task-list' + '/blindBoxStatistics']"
                [queryParams]="{exportType: 'blindBoxStatistics'}"
                vdrDropdownItem
              >
                统计数据记录
              </button>
            </vdr-dropdown-menu>
          </vdr-dropdown> -->
          <!-- <button
            *vdrIfPermissions="['ReadExportTask']"
            [routerLink]="['../export-task-list' + '/blindBoxOrder']"
            [queryParams]="{exportType: 'blindBoxOrder'}"
            class="btn-link"
          >
            导出记录列表
          </button> -->
        </div>
      </form>
    </vdr-ab-left>
  </vdr-action-bar>
  <div class="order-tabs">
    <clr-tabs>
      <clr-tab *ngFor="let item of buyTypeOptions">
        <button
          [class]="{active: item.value === form.value.payStatus}"
          (click)="handleStateFilterBtn(item.value)"
          clrTabLink
        >
          {{ item.label }}
        </button>
      </clr-tab>
    </clr-tabs>
  </div>

  <app-data-table
    [items]="items$ | async"
    [itemsPerPage]="itemsPerPage$ | async"
    [totalItems]="totalItems$ | async"
    [currentPage]="currentPage$ | async"
    class="multi-tr-table"
    (pageChange)="setPageNumber($event)"
    (itemsPerPageChange)="handlePerPageChange($event)"
  >
    <vdr-dt-column>开盒编号</vdr-dt-column>
    <vdr-dt-column>活动信息</vdr-dt-column>
    <vdr-dt-column>盲盒价格</vdr-dt-column>
    <vdr-dt-column>买家/手机号</vdr-dt-column>
    <!-- <vdr-dt-column>提货商品信息</vdr-dt-column> -->
    <vdr-dt-column>支付状态</vdr-dt-column>
    <vdr-dt-column>开盒时间</vdr-dt-column>
    <vdr-dt-column>操作</vdr-dt-column>
    <ng-template let-item="item">
      <!-- <tr class="top-tr">
        <td class="left" colspan="10">
          <div class="flex" style="gap: 12px">
            <span>开盒编号：{{ item.code }}</span>
            <span>开盒时间：{{ item.paymentAt | fmDate }}</span>
          </div>
        </td>
      </tr> -->
      <tr>
        <td class="align-middle">{{ item.wishBoxActivityRecord?.code }}</td>
        <td class="align-middle">
          {{ item.activity?.name }}
        </td>
        <td class="align-middle aftersale-td">
          {{ item?.price | tokenCNY }}
        </td>
        <td class="left align-middle">
          <div class="table-customer-info">
            <div>
              买家：
              <customer-info-dialog-trigger
                *vdrIfPermissions="['ReadCustomer']"
                [locals]="{id: item.customer?.id}"
                size="xl"
              >
                <span class="link-blue">{{ item.customer?.lastName }}</span>
              </customer-info-dialog-trigger>
            </div>
            <div>手机号：{{ item.customer?.phoneNumber }}</div>
          </div>
        </td>
        <!-- <td class="left align-middle aftersale-td">
          <div *ngIf="item.pickupOpenRecord; else noPickUp" class="flex">
            <img
              [src]="
                item.pickupOpenRecord.blindBoxItem.productVariant?.featuredAsset?.preview ??
                item.pickupOpenRecord.blindBoxItem.productVariant?.product?.featuredAsset?.preview
              "
              class="product-preview"
            />
            <div class="ml2">
              <div>{{ item.pickupOpenRecord.blindBoxItem.productVariant?.product?.name }}</div>
              <div>规格：{{ getPickUpVariantOptions(item.pickupOpenRecord.blindBoxItem.productVariant?.options) }}</div>
              <div>价格：{{ item.pickupOpenRecord.blindBoxItem.productVariant?.price | tokenCNY }}</div>
            </div>
          </div>
          <ng-template #noPickUp> 未提货，暂无商品信息 </ng-template>
        </td> -->
        <td class="align-middle">{{ wishboxStatus[item.payStatus] }}</td>
        <td class="align-middle">{{ item.paymentAt | fmDate }}</td>
        <td class="align-middle">
          <button class="btn btn-link" [routerLink]="['./' + item.id]">订单详情</button>
          <button *ngIf="refundable(item.payStatus)" class="btn btn-link" (click)="refundBuy(item.id)">退款</button>
        </td>
      </tr>
    </ng-template>
  </app-data-table>
</vdr-page-block>
