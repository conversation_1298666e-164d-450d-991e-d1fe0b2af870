<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left></vdr-ab-left>
    <vdr-ab-right>
      <button *ngIf="refundable" class="btn" (click)="handleRefund()">主动退款</button>
    </vdr-ab-right>
  </vdr-action-bar>

  <div class="order-detail-top">
    <div class="card detail-top">
      <div *ngIf="orderInfo" class="card-header c-header flex justify-between">
        <div>{{ blindboxStatus[orderInfo.payStatus] }}</div>
        <!-- <button *ngIf="orderInfo?.state === 'PaymentSettled'" class="btn btn-primary" [clrLoading]="isRequesting">执行订单</button> -->
      </div>
    </div>
  </div>

  <div class="clr-row">
    <div class="clr-col-md-8">
      <div *ngIf="openRecords?.wishBoxActivityRecordItems?.length" class="card">
        <div class="card-header c-header">用户商品提货记录</div>
        <div class="card-block p0 mt0">
          <table class="table" style="margin-top: 0px">
            <thead>
              <tr>
                <th>开盒次数</th>
                <th>商品名称</th>
                <th>商品图片</th>
                <th>商品金额</th>
                <th>商品数量</th>
                <th>开盒时间</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let record of openRecords.wishBoxActivityRecordItems; index as i">
                <td class="align-middle">第{{ i + 1 }}次</td>
                <td class="align-middle">{{ record?.productVariant?.name }}</td>
                <td>
                  <img
                    class="product-preview"
                    [src]="
                      record?.productVariant?.featuredAsset?.preview ??
                      record?.productVariant?.product?.featuredAsset?.preview
                    "
                  />
                </td>
                <td class="align-middle">{{ record?.productVariant?.price | tokenCNY }}</td>
                <td class="align-middle">1</td>
                <td class="align-middle">{{ record.createdAt | fmDate }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="clr-col-md-4">
      <div *ngIf="orderInfo" class="card">
        <div class="card-block">
          <div class="c-header">开盒记录编号</div>
          <div>{{ orderInfo.wishBoxActivityRecord?.code }}</div>
          <div class="c-header">支付开盒时间</div>
          <div>{{ orderInfo.paymentAt | fmDate }}</div>
          <div class="c-header">盲盒支付金额</div>
          <div>{{ orderInfo.paymentMetadata?.payerTotal | tokenCNY }}</div>
          <ng-container *ngIf="orderInfo.pickupAt">
            <div class="c-header">提货时间</div>
            <div>{{ orderInfo.pickupAt | fmDate }}</div>
          </ng-container>
          <ng-container *ngIf="orderInfo.blindBoxRefundRecord">
            <div class="c-header">申请退款时间</div>
            <div>{{ orderInfo.blindBoxRefundRecord.refundAt | fmDate }}</div>
          </ng-container>
          <ng-container *ngIf="orderInfo.refundReason">
            <div class="c-header">申请退款原因</div>
            <div>{{ orderInfo.refundReason }}</div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</vdr-page-block>
