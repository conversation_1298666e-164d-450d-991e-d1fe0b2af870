# Cache
.eslintcache
.idea
.npm
.vscode
.yarnclean

# Yarn
.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*

node_modules
npm-debug.log
.DS_Store
coverage
.nyc_output
**/*.tgz
**/package
.sandbox
*.tsbuildinfo
junit.xml
logs

# ignore dist conetents but dist directory for `flat` modules
packages/*/dist/*
apps/*/dist/*
examples/*/dist/*
# .env
apps/server/static/assets
*.sqlite
apps/server/admin-ui
apps/server/admin-*

*.local.*
*plop*

tasks/*
docs/*
deploy-dev.js
deploy-prod*

backup-*.js

