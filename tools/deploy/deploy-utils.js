const fs = require('fs').promises;
const path = require('path');
const {exec} = require('child_process');
const {format} = require('date-fns');
const util = require('util');

const execPromise = util.promisify(exec);

// 日志记录函数
function log(message, type = 'INFO') {
  const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
  const prefix = `[${timestamp}] [${type}]`;
  console.log(`${prefix} ${message}`);
}

// 错误日志记录函数
function logError(message, error = null) {
  log(message, 'ERROR');
  if (error) {
    console.error('详细错误信息:', error);
  }
}

// 解析远程路径，提取用户@主机和路径
function parseRemotePath(remotePath) {
  const match = remotePath.match(/^(.+@.+):(.+)$/);
  if (!match) {
    throw new Error(`无效的远程路径格式: ${remotePath}`);
  }
  return {
    host: match[1],
    path: match[2],
  };
}

// 创建远程目录
async function createRemoteDirectory(remotePath) {
  try {
    const {host, path: remoteDirPath} = parseRemotePath(remotePath);
    const parentDir = path.dirname(remoteDirPath);

    log(`检查并创建远程目录: ${parentDir}`);

    // 使用SSH命令创建目录（-p参数确保创建父目录）
    const command = `ssh ${host} "mkdir -p '${parentDir}'"`;
    log(`执行命令: ${command}`);

    const {stdout, stderr} = await execPromise(command);

    if (stderr && !stderr.includes('Warning')) {
      throw new Error(`SSH命令执行失败: ${stderr}`);
    }

    log(`远程目录创建成功: ${parentDir}`);
    return true;
  } catch (error) {
    logError(`创建远程目录失败: ${remotePath}`, error);
    throw error;
  }
}

// 检查本地目录是否存在
async function checkLocalDirectory(localPath) {
  try {
    const stats = await fs.stat(localPath);
    if (!stats.isDirectory()) {
      throw new Error(`路径不是目录: ${localPath}`);
    }
    log(`本地目录检查通过: ${localPath}`);
    return true;
  } catch (error) {
    logError(`本地目录检查失败: ${localPath}`, error);
    throw error;
  }
}

// 复制目录到远程服务器
async function copyDirectoryToRemote(localPath, remotePath) {
  try {
    log(`开始复制目录: ${localPath} -> ${remotePath}`);

    // 使用rsync命令复制目录
    // -a: 归档模式，保持文件属性
    // -v: 详细输出
    // -z: 压缩传输
    // --delete: 删除目标目录中源目录没有的文件
    // --exclude: 排除不需要的文件
    const command = `rsync -avz --delete --exclude='.DS_Store' --exclude='node_modules' --exclude='.git' "${localPath}/" "${remotePath}/"`;

    log(`执行复制命令: ${command}`);

    const {stdout, stderr} = await execPromise(command, {
      maxBuffer: 1024 * 1024 * 10, // 10MB buffer
    });

    if (stderr && !stderr.includes('Warning')) {
      throw new Error(`rsync命令执行失败: ${stderr}`);
    }

    // 输出rsync的详细信息
    if (stdout) {
      log('复制详情:');
      console.log(stdout);
    }

    log(`目录复制成功: ${localPath} -> ${remotePath}`);
    return true;
  } catch (error) {
    logError(`目录复制失败: ${localPath} -> ${remotePath}`, error);
    throw error;
  }
}

// 部署单个映射
async function deploySingleMapping(localPath, remotePaths) {
  try {
    log(`\n=== 开始部署映射 ===`);
    log(`本地路径: ${localPath}`);
    log(`远程路径: ${remotePaths.join(', ')}`);

    // 1. 检查本地目录
    await checkLocalDirectory(localPath);

    // 2. 为每个远程路径创建目录并复制文件
    for (const remotePath of remotePaths) {
      log(`\n--- 处理远程路径: ${remotePath} ---`);

      // 创建远程目录
      await createRemoteDirectory(remotePath);

      // 复制目录
      await copyDirectoryToRemote(localPath, remotePath);
    }

    log(`\n=== 映射部署完成 ===`);
    return true;
  } catch (error) {
    logError(`映射部署失败: ${localPath}`, error);
    throw error; // 重新抛出错误，中断后续操作
  }
}

// 主部署函数
async function deployWithMappings(mappings, environment = 'unknown') {
  const startTime = new Date();
  log(`\n🚀 开始${environment}环境部署流程 - ${format(startTime, 'yyyy-MM-dd HH:mm:ss')}`);

  try {
    const mappingEntries = Object.entries(mappings);
    log(`共有 ${mappingEntries.length} 个映射需要部署`);

    let completedCount = 0;

    // 逐个处理映射，如果任何一个失败就中断
    for (const [localPath, remotePaths] of mappingEntries) {
      log(`\n📦 处理映射 ${completedCount + 1}/${mappingEntries.length}`);

      try {
        await deploySingleMapping(localPath, remotePaths);
        completedCount++;
        log(`✅ 映射 ${completedCount}/${mappingEntries.length} 部署成功`);
      } catch (error) {
        logError(`❌ 映射 ${completedCount + 1}/${mappingEntries.length} 部署失败，中断后续操作`, error);
        throw error; // 中断整个部署流程
      }
    }

    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);

    log(`\n🎉 ${environment}环境所有部署任务完成！`);
    log(`✅ 成功部署 ${completedCount}/${mappingEntries.length} 个映射`);
    log(`⏱️  总耗时: ${duration} 秒`);
    log(`🕐 完成时间: ${format(endTime, 'yyyy-MM-dd HH:mm:ss')}`);

    return true;
  } catch (error) {
    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);

    logError(`\n💥 ${environment}环境部署流程失败！`, error);
    log(`⏱️  失败前耗时: ${duration} 秒`);
    log(`🕐 失败时间: ${format(endTime, 'yyyy-MM-dd HH:mm:ss')}`);

    throw error; // 重新抛出错误供调用方处理
  }
}

// 导出所有函数
module.exports = {
  log,
  logError,
  parseRemotePath,
  createRemoteDirectory,
  checkLocalDirectory,
  copyDirectoryToRemote,
  deploySingleMapping,
  deployWithMappings
};
